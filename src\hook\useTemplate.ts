import { GetCategoryTemplate, GetMeteringUnitSelectOption } from '@/servers/SupplierProduceStock'
import useCommonOptionStore from '@/store/modules/commonOption'
import { Radio } from 'ant-design-vue'
import dayjs, { type Dayjs } from 'dayjs'
import { filterOption } from '@/utils'
import { type SelectProps } from 'ant-design-vue'
import { Decimal } from 'decimal.js'
import { type BaseFormItem } from './useBaseForm'

/**
 * 获取模板
 * @param slotCallback 自定义插槽回调
 * @returns 模板配置
 */
export const useTemplate = (slotCallback: (item: any) => BaseFormItem, formRef?: Ref<any>) => {
  // 公共选项
  const commonOptionStore = useCommonOptionStore()
  // PLM枚举
  const plmEnum = ref<any>({})
  // 单位选项
  const unitOption = ref<SelectProps['options']>([])

  /**
   * 获取模板
   * @param id 类目ID
   * @returns [ 转换后的模板配置, 模板配置 ]
   */
  const getTemplate = async (id: number) => {
    await Promise.all([getUnit(), getPlmEnum()])
    const res = await GetCategoryTemplate({ id })
    return [transformConfig(res.data.content), res.data.content]
  }

  // 获取单位
  const getUnit = async () => {
    const res: any = await GetMeteringUnitSelectOption()
    unitOption.value = res.data.map((i) => ({
      label: i.description ? `${i.metering_unit_name}(${i.description})` : i.metering_unit_name,
      value: i.id,
    }))
  }
  // 获取PLM枚举
  const getPlmEnum = async () => {
    plmEnum.value = (await commonOptionStore.getPlmEnum())?.attr?.currency_symbol_list || []
  }

  // 禁用日期
  const disabledDate = (current: Dayjs) => current && current < dayjs().endOf('day')
  // 格式化类型
  const formatType = (type: number) => {
    switch (type) {
      case 1:
        return 'YYYY-MM-DD HH:mm:ss'
      case 2:
        return 'YYYY-MM'
      case 3:
        return 'YYYY-MM-DD'
      case 4:
        return 'HH:mm:ss'
      default:
    }
  }

  // 查找单位
  const findUnit = (unit: string, type: number) => {
    const unitName = unitOption.value?.find((i) => i.value === unit)?.label || ''
    if (type === 1) {
      return h('div', {}, unitName)
    }
    const currencySymbol = plmEnum.value.find((i) => i.value === unit)?.label || ''
    return h('div', {}, currencySymbol)
  }

  // 转换配置
  const transformConfig = (config: any[], form?: Ref<any>) => {
    const resList: BaseFormItem[] = []
    config.forEach((i) => {
      if (i?.children instanceof Array && i.children.length) {
        const sum = i.children.reduce((acc, cur) => {
          if ((cur.type === 12 && cur.available_reference_type === 4) || [9, 10, 11].includes(cur.type)) {
            return acc + 1
          }
          return acc
        }, 0)
        if (sum !== i.children.length) {
          resList.push({ label: i.is_customize ? i.language_config[0].attr_name : i.name, type: 'title' })
        }
      }
      if (i.children) {
        const obj: any = {}
        i.children.forEach((unitItem) => {
          if (unitItem.name.includes('长')) {
            obj.long = unitItem.id
          } else if (unitItem.name.includes('宽')) {
            obj.width = unitItem.id
          } else if (unitItem.name.includes('高')) {
            obj.height = unitItem.id
          } else if (unitItem.name.includes('面积')) {
            obj.area = unitItem.id
          } else if (unitItem.name.includes('体积')) {
            obj.volume = unitItem.id
          }
        })
        i.children.forEach((j: any) => {
          if (j.type_json) {
            j.type_json = JSON.parse(j.type_json)
          }
          switch (j.type) {
            case 1:
            case 8:
              resList.push({
                label: j.name,
                type: 'input',
                span: j.w * 6,
                key: j.id,
                props: {
                  allowClear: true,
                  showCount: !!j.type_json.word_limit_count,
                  maxlength: j.type_json.word_limit_count,
                },
              })
              break
            case 2:
              resList.push({
                label: j.name,
                type: 'textarea',
                span: j.w * 6,
                key: j.id,
                props: {
                  showCount: !!j.type_json.word_limit_count,
                  maxlength: j.type_json.word_limit_count,
                },
              })
              break
            // 数值
            case 3:
              resList.push({
                label: j.name,
                type: 'a-input-number',
                span: j.w * 6,
                key: j.id,
                props: {
                  parser: (val: string) => (j.type_json.display_format == 1 ? val.replace(/,/g, '') : val),
                  controls: false,
                  onChange: () => {
                    if (form && form.value[j.id] < j.type_json.min_value) {
                      form.value[j.id] = j.type_json.min_value
                    } else if (form && form.value[j.id] > j.type_json.max_value) {
                      form.value[j.id] = j.type_json.max_value
                    }
                  },
                },
              })
              break
            case 4:
              resList.push({
                label: j.name,
                type: 'a-input-number',
                span: j.w * 6,
                key: j.id,
                props: {
                  parser: (val: string) => (j.type_json.display_format == 1 ? val.replace(/,/g, '') : val),
                  controls: false,
                  addonAfter: findUnit(j.template_display_unit, j.type_json.unit_type),
                  onChange: () => {
                    if (form && form.value[j.id] < j.type_json.min_value) {
                      form.value[j.id] = j.type_json.min_value
                    } else if (form && form.value[j.id] > j.type_json.max_value) {
                      form.value[j.id] = j.type_json.max_value
                    }
                    if ([obj.long, obj.width].includes(j.id) && form) {
                      form.value[obj.area] = new Decimal(form.value[obj.long] || 0).mul(form.value[obj.width] || 0).toNumber()
                      nextTick(() => {
                        validateField(obj.area)
                      })
                    }
                    if ([obj.long, obj.width, obj.height].includes(j.id) && form) {
                      form.value[obj.volume] = new Decimal(form.value[obj.long] || 0)
                        .mul(form.value[obj.width] || 0)
                        .mul(form.value[obj.height] || 0)
                        .toNumber()
                      nextTick(() => {
                        validateField(obj.volume)
                      })
                    }
                  },
                },
              })
              break
            case 5:
              resList.push({
                label: j.name,
                type: 'date-picker',
                span: j.w * 6,
                key: j.id,
                props: {
                  getPopupContainer: (trigger: HTMLElement) => trigger.parentNode as HTMLElement,
                  disabledDate: j.type_json.time_limit ? disabledDate : undefined,
                  valueFormat: formatType(j.type_json.style_type),
                },
              })
              break
            case 6:
              if (j.display_type === 1) {
                resList.push({
                  type: 'radio-group',
                  label: j.name,
                  key: j.id,
                  span: j.w * 6,
                  slots: {
                    default: () => j.type_json.option_list.map((i: any) => h(Radio, { value: i.value }, { default: () => i.option_label_list[0].label })),
                  },
                })
              } else {
                resList.push({
                  type: 'select',
                  label: j.name,
                  key: j.id,
                  span: j.w * 6,
                  props: {
                    class: 'w-full',
                    allowClear: true,
                    options: j.type_json.option_list.map((i: any) => ({ label: i.option_label_list[0].label, value: i.value })),
                  },
                })
              }
              break
            case 7:
              if (j.display_type === 1) {
                resList.push({
                  type: 'checkbox-group',
                  label: j.name,
                  key: j.id,
                  span: j.w * 6,
                  props: {
                    options: j.type_json.option_list,
                  },
                })
              } else if (j.display_type === 2) {
                resList.push({
                  type: 'select',
                  label: j.name,
                  key: j.id,
                  span: j.w * 6,
                  props: {
                    class: 'w-full',
                    allowClear: true,
                    mode: 'multiple',
                    options: j.type_json.option_list.map((i: any) => ({ label: i.option_label_list[0].label, value: i.value })),
                  },
                })
              }

              break
            case 12: {
              let options = []
              if (j?.type_json?.options instanceof Array && j?.type_json?.options.length) {
                options = j?.type_json?.options![0]?.options || []
              } else if (j?.multi_attr_value_options instanceof Array && j?.multi_attr_value_options.length) {
                options = (j?.multi_attr_value_options![0].options || []).map((i) => ({
                  label: i.label || i.mult_attr_value_name,
                  value: i.value || i.id,
                }))
              }
              if (j.type_json.available_reference_type === 1) {
                resList.push({
                  type: 'select',
                  label: j.name,
                  key: j.id,
                  span: j.w * 6,
                  props: {
                    showSearch: true,
                    mode: j.type_json.option_type === 2 ? 'multiple' : undefined,
                    filterOption,
                    allowClear: true,
                    options,
                    getPopupContainer: (trigger: HTMLElement) => trigger.parentNode as HTMLElement,
                  },
                })
              } else if ([2, 3].includes(j.type_json.available_reference_type)) {
                resList.push({
                  type: 'select',
                  label: j.name,
                  key: j.id,
                  span: j.w * 6,
                  props: {
                    showSearch: false,
                    allowClear: true,
                    options,
                    mode: j.type_json.option_type === 2 ? 'multiple' : undefined,
                    getPopupContainer: (trigger: HTMLElement) => trigger.parentNode as HTMLElement,
                  },
                })
              } else {
                if (j.type_json.available_reference_type === 1) {
                  // 逻辑需完善
                }
              }
              break
            }
            default:
              break
          }
        })
      }
      if (i.type === 100) {
        resList.push(slotCallback(i))
      }
    })
    return resList
  }

  const validateField = (key: string) => {
    formRef?.value?.validateFields([key])
  }

  return {
    getTemplate,
    findUnit,
  }
}
